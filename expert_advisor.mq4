//+------------------------------------------------------------------+
//|                   إكسبرت ديناميكي للتداول الآلي                  |
//+------------------------------------------------------------------+
#property copyright "Dynamic Trading Expert"
#property link      "https://www.mql5.com"
#property version   "2.00"
#property strict

//+------------------------------------------------------------------+
//|                     إعدادات المؤشرات                             |
//+------------------------------------------------------------------+
enum EXECUTION_MODE {
    تنفيذ_مباشر = 0,
    بعد_الإغلاق = 1
};

enum INDICATOR_TYPE {
    دخول = 0,
    خروج = 1
};

//--- إعدادات المؤشر الأول ---
extern string Indicator1 = ""; // اتركه فارغاً لتعطيل المؤشر
extern INDICATOR_TYPE IndicatorType1 = دخول;
extern int BuyBuffer1 = 0;
extern int SellBuffer1 = 1;
extern ENUM_TIMEFRAMES TimeFrame1 = PERIOD_CURRENT;
extern EXECUTION_MODE ExecutionMode1 = تنفيذ_مباشر;

//--- إعدادات المؤشر الثاني ---
extern string Indicator2 = ""; // اتركه فارغاً لتعطيل المؤشر
extern INDICATOR_TYPE IndicatorType2 = دخول;
extern int BuyBuffer2 = 0;
extern int SellBuffer2 = 1;
extern ENUM_TIMEFRAMES TimeFrame2 = PERIOD_CURRENT;
extern EXECUTION_MODE ExecutionMode2 = تنفيذ_مباشر;

//--- إعدادات المؤشر الثالث ---
extern string Indicator3 = ""; // اتركه فارغاً لتعطيل المؤشر
extern INDICATOR_TYPE IndicatorType3 = دخول;
extern int BuyBuffer3 = 0;
extern int SellBuffer3 = 1;
extern ENUM_TIMEFRAMES TimeFrame3 = PERIOD_CURRENT;
extern EXECUTION_MODE ExecutionMode3 = تنفيذ_مباشر;

//--- إعدادات المؤشر الرابع ---
extern string Indicator4 = ""; // اتركه فارغاً لتعطيل المؤشر
extern INDICATOR_TYPE IndicatorType4 = دخول;
extern int BuyBuffer4 = 0;
extern int SellBuffer4 = 1;
extern ENUM_TIMEFRAMES TimeFrame4 = PERIOD_CURRENT;
extern EXECUTION_MODE ExecutionMode4 = تنفيذ_مباشر;

//--- إعدادات المؤشر الخامس ---
extern string Indicator5 = ""; // اتركه فارغاً لتعطيل المؤشر
extern INDICATOR_TYPE IndicatorType5 = دخول;
extern int BuyBuffer5 = 0;
extern int SellBuffer5 = 1;
extern ENUM_TIMEFRAMES TimeFrame5 = PERIOD_CURRENT;
extern EXECUTION_MODE ExecutionMode5 = تنفيذ_مباشر;

//+------------------------------------------------------------------+
//|                     الإعدادات العامة                            |
//+------------------------------------------------------------------+
extern string EA_Comment = "Dynamic EA"; // تعليق الصفقات
extern int MagicNumber = 12345; // الرقم السحري للإكسبرت
extern double LotSize = 0.1; // حجم اللوت الأساسي
extern int StopLoss = 100; // وقف الخسارة بالنقاط
extern int TakeProfit = 200; // جني الأرباح بالنقاط
extern int Slippage = 3; // الانزلاق المسموح

//--- إعدادات الصفقة المعاكسة ---
extern bool EnableCounterTrade = false; // تفعيل الصفقة المعاكسة
extern double CounterTradeLotSize = 0.1; // حجم الصفقة المعاكسة

//--- إعدادات إغلاق الصفقات ---
enum CLOSE_SIGNAL_SOURCE {
    لا_يوجد = 0,
    مؤشر1 = 1,
    مؤشر2 = 2,
    مؤشر3 = 3,
    مؤشر4 = 4,
    مؤشر5 = 5,
    الأرباح_والخسائر = 6
};
extern CLOSE_SIGNAL_SOURCE CloseOnOppositeSignal = لا_يوجد;
extern bool EnableProfitLossClose = false; // تفعيل الإغلاق بالربح/الخسارة
extern double ProfitClose = 100; // إغلاق عند الربح
extern double LossClose = -50; // إغلاق عند الخسارة

//--- إعدادات التداول ---
extern bool AllowBuyTrades = true; // السماح بصفقات الشراء
extern bool AllowSellTrades = true; // السماح بصفقات البيع
extern bool OneTradePerBar = true; // صفقة واحدة لكل شمعة
extern int MaxOpenTrades = 1; // الحد الأقصى للصفقات المفتوحة

//+------------------------------------------------------------------+
//|               إعدادات المضاعفة (Double Order)                   |
//+------------------------------------------------------------------+
extern bool EnableDoubleOrder = false; // تفعيل صفقة المضاعفة
extern double DoubleLotSize = 0.2; // حجم اللوت للصفقة المضاعفة

enum DOUBLE_ORDER_TRIGGER {
    بعد_الرصيد = 0,
    بعد_عدد_الصفقات = 1,
    بعد_الخسارة = 2
};
extern DOUBLE_ORDER_TRIGGER DoubleOrderTrigger = بعد_الرصيد;
extern double DoubleOrderBalance = 1000; // الرصيد المطلوب لفتح صفقة مضاعفة
extern int DoubleOrderTradesCount = 3; // عدد الصفقات قبل فتح صفقة مضاعفة
extern bool DoubleOrderSameDirection = true; // صفقة مضاعفة بنفس الاتجاه أم عكسه

//--- إعدادات إدارة المخاطر ---
extern bool EnableRiskManagement = false; // تفعيل إدارة المخاطر
extern double MaxRiskPercent = 2.0; // أقصى مخاطرة كنسبة مئوية من الرصيد
extern double MaxDailyLoss = 100.0; // أقصى خسارة يومية
extern double MaxDailyProfit = 500.0; // أقصى ربح يومي (إيقاف التداول عند الوصول)

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
datetime g_LastBarTime = 0; // وقت آخر شمعة تم التداول عليها
int g_TradesSinceLastDouble = 0; // عدد الصفقات منذ آخر صفقة مضاعفة
datetime g_LastDoubleOrderTime = 0; // وقت آخر صفقة مضاعفة
int g_LastTotalTrades = 0; // إجمالي الصفقات المغلقة
bool g_PendingDoubleOrder = false; // في انتظار فتح صفقة مضاعفة
int g_LastLossOrderType = -1; // نوع آخر صفقة خاسرة
double g_DailyProfit = 0; // الربح اليومي
datetime g_LastDayCheck = 0; // آخر يوم تم فحصه
bool g_TradingAllowed = true; // السماح بالتداول

//--- متغيرات إشارات المؤشرات ---
struct IndicatorSignals {
    bool buySignal;
    bool sellSignal;
    bool isActive;
};

IndicatorSignals g_EntrySignals; // إشارات الدخول المجمعة
IndicatorSignals g_ExitSignals; // إشارات الخروج المجمعة

//--- متغيرات نظام Square of 9 ---
struct SquareOf9Levels {
    double buyLevel;
    double sellLevel;
    bool isActive;
    bool buyLevelUsed;
    bool sellLevelUsed;
    datetime signalBarTime;
    string indicatorName;
};

SquareOf9Levels g_SquareLevels[5]; // مستويات لكل مؤشر
bool g_PendingCounterTrade = false; // في انتظار فتح صفقة معاكسة
int g_CounterTradeType = -1; // نوع الصفقة المعاكسة المطلوبة

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // التحقق من صحة الإعدادات
    if(!ValidateSettings()) {
        return INIT_PARAMETERS_INCORRECT;
    }

    // تهيئة المتغيرات العامة
    InitializeGlobalVariables();

    // طباعة معلومات الإعدادات
    PrintInitializationInfo();

    // عرض توضيحي لحسابات Square of 9
    DemonstrateSquareOf9Calculation();

    Print("=== تم تهيئة الإكسبرت الديناميكي بنجاح ===");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("=== تم إيقاف الإكسبرت - السبب: ", reason, " ===");
}

//+------------------------------------------------------------------+
//| التحقق من صحة الإعدادات                                         |
//+------------------------------------------------------------------+
bool ValidateSettings()
{
    // التحقق من وجود مؤشر واحد على الأقل للدخول
    bool hasEntryIndicator = false;
    if(Indicator1 != "" && IndicatorType1 == دخول) hasEntryIndicator = true;
    if(Indicator2 != "" && IndicatorType2 == دخول) hasEntryIndicator = true;
    if(Indicator3 != "" && IndicatorType3 == دخول) hasEntryIndicator = true;
    if(Indicator4 != "" && IndicatorType4 == دخول) hasEntryIndicator = true;
    if(Indicator5 != "" && IndicatorType5 == دخول) hasEntryIndicator = true;

    if(!hasEntryIndicator) {
        Alert("خطأ: يجب تحديد مؤشر واحد على الأقل للدخول!");
        return false;
    }

    // التحقق من إعدادات اللوت
    if(LotSize <= 0) {
        Alert("خطأ: حجم اللوت يجب أن يكون أكبر من صفر!");
        return false;
    }

    if(EnableDoubleOrder && DoubleLotSize <= 0) {
        Alert("خطأ: حجم اللوت المضاعف يجب أن يكون أكبر من صفر!");
        return false;
    }

    // التحقق من إعدادات إدارة المخاطر
    if(EnableRiskManagement && MaxRiskPercent <= 0) {
        Alert("خطأ: نسبة المخاطرة يجب أن تكون أكبر من صفر!");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| تهيئة المتغيرات العامة                                           |
//+------------------------------------------------------------------+
void InitializeGlobalVariables()
{
    g_LastBarTime = 0;
    g_TradesSinceLastDouble = 0;
    g_LastDoubleOrderTime = 0;
    g_LastTotalTrades = OrdersHistoryTotal();
    g_PendingDoubleOrder = false;
    g_LastLossOrderType = -1;
    g_DailyProfit = 0;
    g_LastDayCheck = TimeCurrent();
    g_TradingAllowed = true;

    // تهيئة إشارات المؤشرات
    g_EntrySignals.buySignal = false;
    g_EntrySignals.sellSignal = false;
    g_EntrySignals.isActive = false;

    g_ExitSignals.buySignal = false;
    g_ExitSignals.sellSignal = false;
    g_ExitSignals.isActive = false;

    // تهيئة مستويات Square of 9
    for(int i = 0; i < 5; i++) {
        g_SquareLevels[i].buyLevel = 0;
        g_SquareLevels[i].sellLevel = 0;
        g_SquareLevels[i].isActive = false;
        g_SquareLevels[i].buyLevelUsed = false;
        g_SquareLevels[i].sellLevelUsed = false;
        g_SquareLevels[i].signalBarTime = 0;
        g_SquareLevels[i].indicatorName = "";
    }

    // تهيئة متغيرات الصفقة المعاكسة
    g_PendingCounterTrade = false;
    g_CounterTradeType = -1;
}

//+------------------------------------------------------------------+
//| طباعة معلومات التهيئة                                           |
//+------------------------------------------------------------------+
void PrintInitializationInfo()
{
    Print("--- إعدادات المؤشرات ---");
    if(Indicator1 != "") Print("المؤشر 1: ", Indicator1, " - النوع: ", (IndicatorType1 == دخول ? "دخول" : "خروج"));
    if(Indicator2 != "") Print("المؤشر 2: ", Indicator2, " - النوع: ", (IndicatorType2 == دخول ? "دخول" : "خروج"));
    if(Indicator3 != "") Print("المؤشر 3: ", Indicator3, " - النوع: ", (IndicatorType3 == دخول ? "دخول" : "خروج"));
    if(Indicator4 != "") Print("المؤشر 4: ", Indicator4, " - النوع: ", (IndicatorType4 == دخول ? "دخول" : "خروج"));
    if(Indicator5 != "") Print("المؤشر 5: ", Indicator5, " - النوع: ", (IndicatorType5 == دخول ? "دخول" : "خروج"));

    Print("--- إعدادات التداول ---");
    Print("حجم اللوت: ", LotSize);
    Print("وقف الخسارة: ", StopLoss, " نقطة");
    Print("جني الأرباح: ", TakeProfit, " نقطة");
    Print("المضاعفة مفعلة: ", EnableDoubleOrder ? "نعم" : "لا");
    if(EnableDoubleOrder) {
        Print("حجم اللوت المضاعف: ", DoubleLotSize);
        string triggerType = "";
        switch(DoubleOrderTrigger) {
            case بعد_الرصيد: triggerType = "بعد الرصيد"; break;
            case بعد_عدد_الصفقات: triggerType = "بعد عدد الصفقات"; break;
            case بعد_الخسارة: triggerType = "بعد الخسارة"; break;
        }
        Print("نوع تفعيل المضاعفة: ", triggerType);
    }
}

//+------------------------------------------------------------------+
//| حساب مستويات Square of 9                                        |
//+------------------------------------------------------------------+
void CalculateSquareOf9Levels(double closePrice, double &buyLevel, double &sellLevel)
{
    // التحقق من صحة السعر المدخل
    if(closePrice <= 0) {
        Print("خطأ: السعر المرجعي يجب أن يكون أكبر من صفر! السعر المدخل: ", closePrice);
        buyLevel = 0;
        sellLevel = 0;
        return;
    }

    // تطبيق معادلة Square of 9 الأصلية
    // المستوى الأول للشراء = السعر المرجعي + 0.25 × √السعر المرجعي
    // المستوى الأول للبيع = السعر المرجعي - 0.25 × √السعر المرجعي

    double sqrtPrice = MathSqrt(closePrice);
    double offset = 0.25 * sqrtPrice;

    buyLevel = closePrice + offset;
    sellLevel = closePrice - offset;

    // تطبيق التقريب المناسب حسب عدد الخانات العشرية للرمز
    int digits = MarketInfo(Symbol(), MODE_DIGITS);
    buyLevel = NormalizeDouble(buyLevel, digits);
    sellLevel = NormalizeDouble(sellLevel, digits);

    // التحقق من صحة الحسابات النهائية باستخدام دالة التحقق
    if(!ValidateSquareOf9Levels(closePrice, buyLevel, sellLevel)) {
        Print("خطأ في حسابات Square of 9! السعر المرجعي: ", closePrice,
              " | مستوى الشراء: ", buyLevel, " | مستوى البيع: ", sellLevel);
        buyLevel = 0;
        sellLevel = 0;
        return;
    }

    // حساب النسب المئوية للمستويات
    double buyPercentage = ((buyLevel - closePrice) / closePrice) * 100;
    double sellPercentage = ((closePrice - sellLevel) / closePrice) * 100;

    Print("Square of 9 - السعر المرجعي: ", closePrice,
          " | مستوى الشراء: ", buyLevel, " (+", DoubleToString(buyPercentage, 3), "%)",
          " | مستوى البيع: ", sellLevel, " (-", DoubleToString(sellPercentage, 3), "%)",
          " | المسافة: ", DoubleToString(buyLevel - sellLevel, digits), " نقطة");
}

//+------------------------------------------------------------------+
//| حساب مستويات Square of 9 المتعددة (للاستخدام المستقبلي)         |
//+------------------------------------------------------------------+
void CalculateMultipleSquareOf9Levels(double closePrice, double &levels[], int numLevels = 3)
{
    if(closePrice <= 0 || numLevels <= 0) return;

    ArrayResize(levels, numLevels * 2); // مستويات شراء وبيع

    double sqrtPrice = MathSqrt(closePrice);
    int digits = MarketInfo(Symbol(), MODE_DIGITS);

    for(int i = 1; i <= numLevels; i++) {
        double offset = (0.25 * i) * sqrtPrice; // مضاعفات المستوى الأول

        // مستويات الشراء (فهرس زوجي)
        levels[(i-1)*2] = NormalizeDouble(closePrice + offset, digits);

        // مستويات البيع (فهرس فردي)
        levels[(i-1)*2 + 1] = NormalizeDouble(closePrice - offset, digits);
    }

    Print("تم حساب ", numLevels, " مستويات Square of 9 للسعر: ", closePrice);
}

//+------------------------------------------------------------------+
//| التحقق من صحة مستويات Square of 9                              |
//+------------------------------------------------------------------+
bool ValidateSquareOf9Levels(double closePrice, double buyLevel, double sellLevel)
{
    if(closePrice <= 0) return false;
    if(buyLevel <= closePrice) return false;
    if(sellLevel >= closePrice) return false;
    if(buyLevel <= sellLevel) return false;

    // التحقق من أن المستويات ضمن نطاق معقول (لا تزيد عن 10% من السعر)
    double maxDeviation = closePrice * 0.10; // 10%
    if((buyLevel - closePrice) > maxDeviation) return false;
    if((closePrice - sellLevel) > maxDeviation) return false;

    return true;
}

//+------------------------------------------------------------------+
//| عرض توضيحي لحسابات Square of 9 (للاختبار)                      |
//+------------------------------------------------------------------+
void DemonstrateSquareOf9Calculation()
{
    double testPrice = 3389.68; // المثال المذكور في الطلب
    double buyLevel, sellLevel;

    Print("=== عرض توضيحي لحسابات Square of 9 ===");
    Print("السعر المرجعي: ", testPrice);

    // حساب الجذر التربيعي
    double sqrtPrice = MathSqrt(testPrice);
    Print("√", testPrice, " = ", DoubleToString(sqrtPrice, 3));

    // حساب الإزاحة
    double offset = 0.25 * sqrtPrice;
    Print("0.25 × ", DoubleToString(sqrtPrice, 3), " = ", DoubleToString(offset, 3));

    // حساب المستويات
    buyLevel = testPrice + offset;
    sellLevel = testPrice - offset;

    Print("مستوى الشراء: ", testPrice, " + ", DoubleToString(offset, 3), " = ", DoubleToString(buyLevel, 2));
    Print("مستوى البيع: ", testPrice, " - ", DoubleToString(offset, 3), " = ", DoubleToString(sellLevel, 2));

    // استخدام الدالة الرسمية للتحقق
    CalculateSquareOf9Levels(testPrice, buyLevel, sellLevel);

    Print("=== انتهاء العرض التوضيحي ===");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // التحقق من السماح بالتداول
    if(!CheckTradingConditions()) {
        return;
    }

    // تحديث الربح اليومي
    UpdateDailyProfit();

    // قراءة وتحليل إشارات المؤشرات
    AnalyzeIndicatorSignals();

    // التحقق من الصفقات المغلقة حديثاً
    CheckForClosedTrades();

    // معالجة إغلاق الصفقات
    ProcessTradeClosing();

    // معالجة مستويات Square of 9
    ProcessSquareOf9Levels();

    // معالجة فتح الصفقات
    ProcessTradeOpening();

    // معالجة الصفقات المضاعفة
    ProcessDoubleOrders();

    // معالجة الصفقة المعاكسة
    ProcessCounterTrade();
}

//+------------------------------------------------------------------+
//| التحقق من شروط التداول                                          |
//+------------------------------------------------------------------+
bool CheckTradingConditions()
{
    // التحقق من إدارة المخاطر
    if(EnableRiskManagement) {
        if(g_DailyProfit <= -MaxDailyLoss) {
            if(g_TradingAllowed) {
                Print("تم إيقاف التداول: تم الوصول للحد الأقصى للخسارة اليومية");
                g_TradingAllowed = false;
            }
            return false;
        }

        if(g_DailyProfit >= MaxDailyProfit) {
            if(g_TradingAllowed) {
                Print("تم إيقاف التداول: تم الوصول للحد الأقصى للربح اليومي");
                g_TradingAllowed = false;
            }
            return false;
        }
    }

    // التحقق من ساعات التداول (يمكن إضافة المزيد من الشروط هنا)
    return true;
}

//+------------------------------------------------------------------+
//| تحديث الربح اليومي                                              |
//+------------------------------------------------------------------+
void UpdateDailyProfit()
{
    datetime currentDay = iTime(Symbol(), PERIOD_D1, 0);

    // إذا كان يوم جديد، إعادة تعيين الربح اليومي
    if(currentDay != g_LastDayCheck) {
        g_DailyProfit = 0;
        g_LastDayCheck = currentDay;
        g_TradingAllowed = true;
        Print("يوم تداول جديد - تم إعادة تعيين الربح اليومي");
    }

    // حساب الربح اليومي الحالي
    g_DailyProfit = CalculateDailyProfit();
}

//+------------------------------------------------------------------+
//| تحليل إشارات المؤشرات                                           |
//+------------------------------------------------------------------+
void AnalyzeIndicatorSignals()
{
    // إعادة تعيين الإشارات
    ResetSignals();

    // تحليل كل مؤشر بشكل مستقل
    AnalyzeIndicatorIndependent(Indicator1, IndicatorType1, BuyBuffer1, SellBuffer1, TimeFrame1, ExecutionMode1, 0);
    AnalyzeIndicatorIndependent(Indicator2, IndicatorType2, BuyBuffer2, SellBuffer2, TimeFrame2, ExecutionMode2, 1);
    AnalyzeIndicatorIndependent(Indicator3, IndicatorType3, BuyBuffer3, SellBuffer3, TimeFrame3, ExecutionMode3, 2);
    AnalyzeIndicatorIndependent(Indicator4, IndicatorType4, BuyBuffer4, SellBuffer4, TimeFrame4, ExecutionMode4, 3);
    AnalyzeIndicatorIndependent(Indicator5, IndicatorType5, BuyBuffer5, SellBuffer5, TimeFrame5, ExecutionMode5, 4);
}

//+------------------------------------------------------------------+
//| إعادة تعيين الإشارات                                             |
//+------------------------------------------------------------------+
void ResetSignals()
{
    g_EntrySignals.buySignal = false;
    g_EntrySignals.sellSignal = false;
    g_EntrySignals.isActive = false;

    g_ExitSignals.buySignal = false;
    g_ExitSignals.sellSignal = false;
    g_ExitSignals.isActive = false;
}

//+------------------------------------------------------------------+
//| تحليل مؤشر واحد بشكل مستقل                                       |
//+------------------------------------------------------------------+
void AnalyzeIndicatorIndependent(string indicatorName, INDICATOR_TYPE indicatorType, int buyBuffer, int sellBuffer,
                                ENUM_TIMEFRAMES timeFrame, EXECUTION_MODE executionMode, int indicatorIndex)
{
    if(indicatorName == "") return; // المؤشر غير مفعل

    int shift = (executionMode == تنفيذ_مباشر) ? 0 : 1;
    double buyValue = iCustom(Symbol(), timeFrame, indicatorName, buyBuffer, shift);
    double sellValue = iCustom(Symbol(), timeFrame, indicatorName, sellBuffer, shift);

    bool hasBuySignal = (buyValue != EMPTY_VALUE && buyValue != 0);
    bool hasSellSignal = (sellValue != EMPTY_VALUE && sellValue != 0);

    if(indicatorType == دخول) {
        // التحقق من إشارة جديدة لم تُعالج من قبل
        datetime currentBarTime = Time[shift];

        if(hasBuySignal && g_SquareLevels[indicatorIndex].signalBarTime != currentBarTime) {
            // إشارة شراء جديدة - انتظار إغلاق الشمعة
            if(shift > 0 || (shift == 0 && Time[0] != Time[1])) { // الشمعة مغلقة
                double closePrice = Close[shift];
                CalculateSquareOf9Levels(closePrice, g_SquareLevels[indicatorIndex].buyLevel, g_SquareLevels[indicatorIndex].sellLevel);
                g_SquareLevels[indicatorIndex].isActive = true;
                g_SquareLevels[indicatorIndex].buyLevelUsed = false;
                g_SquareLevels[indicatorIndex].sellLevelUsed = false;
                g_SquareLevels[indicatorIndex].signalBarTime = currentBarTime;
                g_SquareLevels[indicatorIndex].indicatorName = indicatorName;
                Print("إشارة شراء جديدة من ", indicatorName, " - تم حساب مستويات Square of 9");
            }
        }

        if(hasSellSignal && g_SquareLevels[indicatorIndex].signalBarTime != currentBarTime) {
            // إشارة بيع جديدة - انتظار إغلاق الشمعة
            if(shift > 0 || (shift == 0 && Time[0] != Time[1])) { // الشمعة مغلقة
                double closePrice = Close[shift];
                CalculateSquareOf9Levels(closePrice, g_SquareLevels[indicatorIndex].buyLevel, g_SquareLevels[indicatorIndex].sellLevel);
                g_SquareLevels[indicatorIndex].isActive = true;
                g_SquareLevels[indicatorIndex].buyLevelUsed = false;
                g_SquareLevels[indicatorIndex].sellLevelUsed = false;
                g_SquareLevels[indicatorIndex].signalBarTime = currentBarTime;
                g_SquareLevels[indicatorIndex].indicatorName = indicatorName;
                Print("إشارة بيع جديدة من ", indicatorName, " - تم حساب مستويات Square of 9");
            }
        }

        // تحديث الإشارات العامة للتوافق مع النظام القديم
        if(hasBuySignal || hasSellSignal) {
            g_EntrySignals.isActive = true;
            if(hasBuySignal) g_EntrySignals.buySignal = true;
            if(hasSellSignal) g_EntrySignals.sellSignal = true;
        }
    } else { // خروج
        if(hasBuySignal) g_ExitSignals.buySignal = true;
        if(hasSellSignal) g_ExitSignals.sellSignal = true;
        if(hasBuySignal || hasSellSignal) g_ExitSignals.isActive = true;
    }
}

//+------------------------------------------------------------------+
//| معالجة فتح الصفقات                                               |
//+------------------------------------------------------------------+
void ProcessTradeOpening()
{
    // ملاحظة: فتح الصفقات الآن يتم بشكل أساسي من خلال نظام Square of 9
    // هذه الدالة تُستخدم فقط للحالات الخاصة أو التوافق مع النظام القديم

    // التحقق من شروط فتح الصفقات
    if(!g_EntrySignals.isActive) return;
    if(OneTradePerBar && Time[0] == g_LastBarTime) return;
    if(GetOpenTradesCount() >= MaxOpenTrades) return;

    // التحقق من وجود مستويات Square of 9 نشطة
    bool hasActiveLevels = false;
    for(int i = 0; i < 5; i++) {
        if(g_SquareLevels[i].isActive) {
            hasActiveLevels = true;
            break;
        }
    }

    // إذا كان هناك مستويات Square of 9 نشطة، لا نفتح صفقات مباشرة
    // نترك النظام ينتظر وصول السعر للمستويات المحسوبة
    if(hasActiveLevels) {
        return;
    }

    // فقط في حالة عدم وجود مستويات نشطة، يمكن فتح صفقات مباشرة
    // (هذا للتوافق مع المؤشرات التي لا تستخدم Square of 9)
    Print("تحذير: لا توجد مستويات Square of 9 نشطة - فتح صفقة مباشرة");
}

//+------------------------------------------------------------------+
//| معالجة إغلاق الصفقات                                             |
//+------------------------------------------------------------------+
void ProcessTradeClosing()
{
    bool closeBuy = false;
    bool closeSell = false;

    // إغلاق بإشارات الخروج المباشرة
    if(g_ExitSignals.sellSignal) closeBuy = true;  // إشارة بيع تغلق صفقة الشراء
    if(g_ExitSignals.buySignal) closeSell = true;  // إشارة شراء تغلق صفقة البيع

    // إغلاق بالإشارة المعاكسة من مؤشر محدد
    if(CloseOnOppositeSignal != لا_يوجد) {
        ProcessOppositeSignalClosing(closeBuy, closeSell);
    }

    // إغلاق بالربح/الخسارة
    if(EnableProfitLossClose) {
        ProcessProfitLossClosing(closeBuy, closeSell);
    }

    // تنفيذ الإغلاق
    if(closeBuy || closeSell) {
        CloseOrders(closeBuy, closeSell);
    }
}

//+------------------------------------------------------------------+
//| معالجة الإغلاق بالإشارة المعاكسة                                 |
//+------------------------------------------------------------------+
void ProcessOppositeSignalClosing(bool &closeBuy, bool &closeSell)
{
    string targetIndicator = "";
    INDICATOR_TYPE targetType;
    int targetBuyBuffer, targetSellBuffer;
    ENUM_TIMEFRAMES targetTimeFrame;
    EXECUTION_MODE targetExecutionMode;

    // تحديد المؤشر المستهدف
    switch(CloseOnOppositeSignal) {
        case مؤشر1:
            targetIndicator = Indicator1;
            targetType = IndicatorType1;
            targetBuyBuffer = BuyBuffer1;
            targetSellBuffer = SellBuffer1;
            targetTimeFrame = TimeFrame1;
            targetExecutionMode = ExecutionMode1;
            break;
        case مؤشر2:
            targetIndicator = Indicator2;
            targetType = IndicatorType2;
            targetBuyBuffer = BuyBuffer2;
            targetSellBuffer = SellBuffer2;
            targetTimeFrame = TimeFrame2;
            targetExecutionMode = ExecutionMode2;
            break;
        case مؤشر3:
            targetIndicator = Indicator3;
            targetType = IndicatorType3;
            targetBuyBuffer = BuyBuffer3;
            targetSellBuffer = SellBuffer3;
            targetTimeFrame = TimeFrame3;
            targetExecutionMode = ExecutionMode3;
            break;
        case مؤشر4:
            targetIndicator = Indicator4;
            targetType = IndicatorType4;
            targetBuyBuffer = BuyBuffer4;
            targetSellBuffer = SellBuffer4;
            targetTimeFrame = TimeFrame4;
            targetExecutionMode = ExecutionMode4;
            break;
        case مؤشر5:
            targetIndicator = Indicator5;
            targetType = IndicatorType5;
            targetBuyBuffer = BuyBuffer5;
            targetSellBuffer = SellBuffer5;
            targetTimeFrame = TimeFrame5;
            targetExecutionMode = ExecutionMode5;
            break;
    }

    if(targetIndicator != "") {
        int shift = (targetExecutionMode == تنفيذ_مباشر) ? 0 : 1;
        double buyValue = iCustom(Symbol(), targetTimeFrame, targetIndicator, targetBuyBuffer, shift);
        double sellValue = iCustom(Symbol(), targetTimeFrame, targetIndicator, targetSellBuffer, shift);

        if(sellValue != EMPTY_VALUE && sellValue != 0) closeBuy = true;
        if(buyValue != EMPTY_VALUE && buyValue != 0) closeSell = true;
    }
}

//+------------------------------------------------------------------+
//| معالجة الإغلاق بالربح/الخسارة                                    |
//+------------------------------------------------------------------+
void ProcessProfitLossClosing(bool &closeBuy, bool &closeSell)
{
    double totalProfit = CalculateCurrentProfit();

    if(totalProfit >= ProfitClose || totalProfit <= LossClose) {
        closeBuy = true;
        closeSell = true;
        Print("إغلاق جميع الصفقات - الربح الحالي: ", totalProfit);
    }
}

//+------------------------------------------------------------------+
//| معالجة الصفقات المضاعفة                                          |
//+------------------------------------------------------------------+
void ProcessDoubleOrders()
{
    if(!EnableDoubleOrder) return;

    switch(DoubleOrderTrigger) {
        case بعد_الرصيد:
            ProcessDoubleOrderByBalance();
            break;
        case بعد_عدد_الصفقات:
            ProcessDoubleOrderByTradeCount();
            break;
        case بعد_الخسارة:
            ProcessDoubleOrderAfterLoss();
            break;
    }
}

//+------------------------------------------------------------------+
//| معالجة المضاعفة بعد الوصول للرصيد المطلوب                       |
//+------------------------------------------------------------------+
void ProcessDoubleOrderByBalance()
{
    if(AccountBalance() < DoubleOrderBalance) return;

    // استخدام حجم اللوت المضاعف في الصفقات العادية
    // هذا يتم التعامل معه في دالة CalculateLotSize()
}

//+------------------------------------------------------------------+
//| معالجة المضاعفة بعد عدد معين من الصفقات                         |
//+------------------------------------------------------------------+
void ProcessDoubleOrderByTradeCount()
{
    if(g_TradesSinceLastDouble < DoubleOrderTradesCount) return;
    if(Time[0] == g_LastDoubleOrderTime) return;

    bool orderOpened = false;

    if(g_EntrySignals.buySignal && !IsOrderOpen(OP_BUY)) {
        if(OpenOrder(OP_BUY, DoubleLotSize)) {
            orderOpened = true;
            Print("تم فتح صفقة مضاعفة شراء بعد ", DoubleOrderTradesCount, " صفقات");
        }
    } else if(g_EntrySignals.sellSignal && !IsOrderOpen(OP_SELL)) {
        if(OpenOrder(OP_SELL, DoubleLotSize)) {
            orderOpened = true;
            Print("تم فتح صفقة مضاعفة بيع بعد ", DoubleOrderTradesCount, " صفقات");
        }
    }

    if(orderOpened) {
        g_LastDoubleOrderTime = Time[0];
        g_TradesSinceLastDouble = 0;
    }
}

//+------------------------------------------------------------------+
//| معالجة المضاعفة بعد الخسارة                                      |
//+------------------------------------------------------------------+
void ProcessDoubleOrderAfterLoss()
{
    if(!g_PendingDoubleOrder) return;
    if(Time[0] == g_LastDoubleOrderTime) return;

    bool orderOpened = false;
    int targetOrderType = DoubleOrderSameDirection ? g_LastLossOrderType : (g_LastLossOrderType == OP_BUY ? OP_SELL : OP_BUY);

    if(targetOrderType == OP_BUY && g_EntrySignals.buySignal && !IsOrderOpen(OP_BUY)) {
        if(OpenOrder(OP_BUY, DoubleLotSize)) {
            orderOpened = true;
            Print("تم فتح صفقة مضاعفة شراء بعد خسارة");
        }
    } else if(targetOrderType == OP_SELL && g_EntrySignals.sellSignal && !IsOrderOpen(OP_SELL)) {
        if(OpenOrder(OP_SELL, DoubleLotSize)) {
            orderOpened = true;
            Print("تم فتح صفقة مضاعفة بيع بعد خسارة");
        }
    }

    if(orderOpened) {
        g_LastDoubleOrderTime = Time[0];
        g_PendingDoubleOrder = false;
    }
}

//+------------------------------------------------------------------+
//| معالجة الصفقة المعاكسة                                           |
//+------------------------------------------------------------------+
void ProcessCounterTrade()
{
    if(!EnableCounterTrade) return;
    if(!g_PendingCounterTrade) return;

    // التحقق من وجود إشارة دخول
    if(!g_EntrySignals.isActive) return;

    // تحديد نوع الصفقة المطلوبة
    bool shouldOpenBuy = (g_CounterTradeType == OP_BUY && g_EntrySignals.buySignal);
    bool shouldOpenSell = (g_CounterTradeType == OP_SELL && g_EntrySignals.sellSignal);

    if(shouldOpenBuy && AllowBuyTrades && !IsOrderOpen(OP_BUY)) {
        if(OpenOrder(OP_BUY, CounterTradeLotSize)) {
            g_PendingCounterTrade = false;
            g_CounterTradeType = -1;
            Print("تم فتح صفقة معاكسة شراء");
        }
    } else if(shouldOpenSell && AllowSellTrades && !IsOrderOpen(OP_SELL)) {
        if(OpenOrder(OP_SELL, CounterTradeLotSize)) {
            g_PendingCounterTrade = false;
            g_CounterTradeType = -1;
            Print("تم فتح صفقة معاكسة بيع");
        }
    }
}

//+------------------------------------------------------------------+
//| معالجة مستويات Square of 9                                      |
//+------------------------------------------------------------------+
void ProcessSquareOf9Levels()
{
    double currentPrice = (Ask + Bid) / 2.0; // متوسط السعر الحالي

    // فحص جميع مستويات Square of 9 النشطة
    for(int i = 0; i < 5; i++) {
        if(!g_SquareLevels[i].isActive) continue;

        // التحقق من وصول السعر لمستوى الشراء
        if(!g_SquareLevels[i].buyLevelUsed && currentPrice >= g_SquareLevels[i].buyLevel) {
            if(AllowBuyTrades && !IsOrderOpen(OP_BUY)) {
                double lotSize = CalculateLotSize();
                if(OpenOrder(OP_BUY, lotSize)) {
                    g_SquareLevels[i].buyLevelUsed = true;
                    Print("تم فتح صفقة شراء عند مستوى Square of 9: ", g_SquareLevels[i].buyLevel,
                          " من المؤشر: ", g_SquareLevels[i].indicatorName);
                }
            }
        }

        // التحقق من وصول السعر لمستوى البيع
        if(!g_SquareLevels[i].sellLevelUsed && currentPrice <= g_SquareLevels[i].sellLevel) {
            if(AllowSellTrades && !IsOrderOpen(OP_SELL)) {
                double lotSize = CalculateLotSize();
                if(OpenOrder(OP_SELL, lotSize)) {
                    g_SquareLevels[i].sellLevelUsed = true;
                    Print("تم فتح صفقة بيع عند مستوى Square of 9: ", g_SquareLevels[i].sellLevel,
                          " من المؤشر: ", g_SquareLevels[i].indicatorName);
                }
            }
        }

        // إذا تم استخدام كلا المستويين، إلغاء تفعيل هذا المؤشر حتى إشارة جديدة
        if(g_SquareLevels[i].buyLevelUsed && g_SquareLevels[i].sellLevelUsed) {
            g_SquareLevels[i].isActive = false;
            Print("تم استخدام جميع مستويات Square of 9 للمؤشر: ", g_SquareLevels[i].indicatorName);
        }
    }
}

//+------------------------------------------------------------------+
//| التحقق من الصفقات المغلقة حديثاً                                  |
//+------------------------------------------------------------------+
void CheckForClosedTrades()
{
    int currentTotalTrades = OrdersHistoryTotal();

    // إذا كان هناك صفقات جديدة مغلقة
    if(currentTotalTrades > g_LastTotalTrades) {
        // البحث عن آخر صفقة مغلقة لهذا الرمز
        for(int i = currentTotalTrades - 1; i >= g_LastTotalTrades; i--) {
            if(OrderSelect(i, SELECT_BY_POS, MODE_HISTORY)) {
                if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber) {
                    double profit = OrderProfit() + OrderSwap() + OrderCommission();
                    int orderType = OrderType();

                    // إذا كانت الصفقة خاسرة وتفعيل المضاعفة بعد الخسارة
                    if(profit < 0 && EnableDoubleOrder && DoubleOrderTrigger == بعد_الخسارة) {
                        g_PendingDoubleOrder = true;
                        g_LastLossOrderType = orderType;
                        Print("تم اكتشاف صفقة خاسرة. سيتم فتح صفقة مضاعفة عند الإشارة التالية. الربح: ", profit);
                    }

                    // طباعة معلومات الصفقة المغلقة
                    Print("تم إغلاق صفقة ", (orderType == OP_BUY ? "شراء" : "بيع"),
                          " - الربح: ", profit, " - التذكرة: ", OrderTicket());

                    break; // نأخذ فقط آخر صفقة مغلقة
                }
            }
        }
        g_LastTotalTrades = currentTotalTrades;
    }
}

//+------------------------------------------------------------------+
//| حساب حجم اللوت                                                   |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
    double lotSize = LotSize;

    // إذا كانت المضاعفة مفعلة بعد الوصول للرصيد المطلوب
    if(EnableDoubleOrder && DoubleOrderTrigger == بعد_الرصيد && AccountBalance() >= DoubleOrderBalance) {
        lotSize = DoubleLotSize;
    }

    // تطبيق إدارة المخاطر
    if(EnableRiskManagement) {
        double riskAmount = AccountBalance() * MaxRiskPercent / 100.0;
        double stopLossPoints = StopLoss > 0 ? StopLoss : 100; // قيمة افتراضية
        double riskBasedLot = riskAmount / (stopLossPoints * MarketInfo(Symbol(), MODE_TICKVALUE));

        if(riskBasedLot < lotSize) {
            lotSize = riskBasedLot;
        }
    }

    // التأكد من أن حجم اللوت ضمن الحدود المسموحة
    double minLot = MarketInfo(Symbol(), MODE_MINLOT);
    double maxLot = MarketInfo(Symbol(), MODE_MAXLOT);
    double lotStep = MarketInfo(Symbol(), MODE_LOTSTEP);

    lotSize = MathMax(lotSize, minLot);
    lotSize = MathMin(lotSize, maxLot);
    lotSize = NormalizeDouble(lotSize / lotStep, 0) * lotStep;

    return lotSize;
}

//+------------------------------------------------------------------+
//| حساب عدد الصفقات المفتوحة                                        |
//+------------------------------------------------------------------+
int GetOpenTradesCount()
{
    int count = 0;
    for(int i = 0; i < OrdersTotal(); i++) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber) {
                count++;
            }
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| حساب الربح الحالي لجميع الصفقات المفتوحة                         |
//+------------------------------------------------------------------+
double CalculateCurrentProfit()
{
    double totalProfit = 0;
    for(int i = 0; i < OrdersTotal(); i++) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber) {
                totalProfit += OrderProfit() + OrderSwap() + OrderCommission();
            }
        }
    }
    return totalProfit;
}

//+------------------------------------------------------------------+
//| حساب الربح اليومي                                                |
//+------------------------------------------------------------------+
double CalculateDailyProfit()
{
    double dailyProfit = 0;
    datetime startOfDay = iTime(Symbol(), PERIOD_D1, 0);

    // حساب الربح من الصفقات المغلقة اليوم
    for(int i = OrdersHistoryTotal() - 1; i >= 0; i--) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_HISTORY)) {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber) {
                if(OrderCloseTime() >= startOfDay) {
                    dailyProfit += OrderProfit() + OrderSwap() + OrderCommission();
                } else {
                    break; // الصفقات مرتبة حسب الوقت، لذا يمكننا التوقف هنا
                }
            }
        }
    }

    // إضافة الربح من الصفقات المفتوحة
    dailyProfit += CalculateCurrentProfit();

    return dailyProfit;
}

//+------------------------------------------------------------------+
//| التحقق من وجود صفقة مفتوحة من نوع معين                           |
//+------------------------------------------------------------------+
bool IsOrderOpen(int orderType)
{
    for(int i = 0; i < OrdersTotal(); i++) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
            if(OrderType() == orderType && OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber) {
                return true;
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| فتح صفقة جديدة                                                   |
//+------------------------------------------------------------------+
bool OpenOrder(int type, double lots)
{
    // التحقق من صحة المعاملات
    if(lots <= 0) {
        Print("خطأ: حجم اللوت غير صحيح: ", lots);
        return false;
    }

    double price, sl = 0, tp = 0;
    color orderColor;
    string orderComment = EA_Comment + " v2.0";

    // تحديد السعر ووقف الخسارة وجني الأرباح
    if(type == OP_BUY) {
        price = Ask;
        if(StopLoss > 0) sl = price - StopLoss * Point;
        if(TakeProfit > 0) tp = price + TakeProfit * Point;
        orderColor = clrGreen;
    } else if(type == OP_SELL) {
        price = Bid;
        if(StopLoss > 0) sl = price + StopLoss * Point;
        if(TakeProfit > 0) tp = price - TakeProfit * Point;
        orderColor = clrRed;
    } else {
        Print("خطأ: نوع الصفقة غير مدعوم: ", type);
        return false;
    }

    // تطبيق تعديل النقاط للوسطاء ذوي النقاط الخمس
    double point = Point;
    if(Digits == 5 || Digits == 3) {
        point *= 10;
        if(sl > 0) sl = NormalizeDouble(sl, Digits);
        if(tp > 0) tp = NormalizeDouble(tp, Digits);
    }

    // محاولة فتح الصفقة
    int ticket = OrderSend(Symbol(), type, lots, price, Slippage, sl, tp,
                          orderComment, MagicNumber, 0, orderColor);

    if(ticket > 0) {
        Print("تم فتح صفقة ", (type == OP_BUY ? "شراء" : "بيع"),
              " - التذكرة: ", ticket,
              " - الحجم: ", lots,
              " - السعر: ", price);
        return true;
    } else {
        int error = GetLastError();
        Print("خطأ في فتح الصفقة - كود الخطأ: ", error, " - الوصف: ", ErrorDescription(error));
        return false;
    }
}

//+------------------------------------------------------------------+
//| وصف أخطاء التداول                                               |
//+------------------------------------------------------------------+
string ErrorDescription(int errorCode)
{
    switch(errorCode) {
        case ERR_NO_ERROR: return "لا يوجد خطأ";
        case ERR_NO_RESULT: return "لا يوجد نتيجة";
        case ERR_COMMON_ERROR: return "خطأ عام";
        case ERR_INVALID_TRADE_PARAMETERS: return "معاملات التداول غير صحيحة";
        case ERR_SERVER_BUSY: return "الخادم مشغول";
        case ERR_OLD_VERSION: return "إصدار قديم من العميل";
        case ERR_NO_CONNECTION: return "لا يوجد اتصال";
        case ERR_NOT_ENOUGH_RIGHTS: return "صلاحيات غير كافية";
        case ERR_TOO_FREQUENT_REQUESTS: return "طلبات متكررة جداً";
        case ERR_MALFUNCTIONAL_TRADE: return "عملية تداول معطلة";
        case ERR_ACCOUNT_DISABLED: return "الحساب معطل";
        case ERR_INVALID_ACCOUNT: return "حساب غير صحيح";
        case ERR_TRADE_TIMEOUT: return "انتهت مهلة التداول";
        case ERR_INVALID_PRICE: return "سعر غير صحيح";
        case ERR_INVALID_STOPS: return "مستويات الوقف غير صحيحة";
        case ERR_INVALID_TRADE_VOLUME: return "حجم التداول غير صحيح";
        case ERR_MARKET_CLOSED: return "السوق مغلق";
        case ERR_TRADE_DISABLED: return "التداول معطل";
        case ERR_NOT_ENOUGH_MONEY: return "أموال غير كافية";
        case ERR_PRICE_CHANGED: return "تغير السعر";
        case ERR_OFF_QUOTES: return "خارج الأسعار";
        case ERR_BROKER_BUSY: return "الوسيط مشغول";
        case ERR_REQUOTE: return "إعادة تسعير";
        case ERR_ORDER_LOCKED: return "الطلب مقفل";
        case ERR_LONG_POSITIONS_ONLY_ALLOWED: return "مسموح بصفقات الشراء فقط";
        case ERR_TOO_MANY_REQUESTS: return "طلبات كثيرة جداً";
        default: return "خطأ غير معروف: " + IntegerToString(errorCode);
    }
}

//+------------------------------------------------------------------+
//| إغلاق الصفقات المفتوحة                                           |
//+------------------------------------------------------------------+
void CloseOrders(bool closeBuy, bool closeSell)
{
    for(int i = OrdersTotal() - 1; i >= 0; i--) {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber) {
                bool shouldClose = false;
                double closePrice = 0;

                if(OrderType() == OP_BUY && closeBuy) {
                    shouldClose = true;
                    closePrice = Bid;
                } else if(OrderType() == OP_SELL && closeSell) {
                    shouldClose = true;
                    closePrice = Ask;
                }

                if(shouldClose) {
                    bool result = OrderClose(OrderTicket(), OrderLots(), closePrice, Slippage, clrYellow);
                    if(result) {
                        Print("تم إغلاق صفقة ", (OrderType() == OP_BUY ? "شراء" : "بيع"),
                              " - التذكرة: ", OrderTicket(),
                              " - الربح: ", OrderProfit());
                    } else {
                        int error = GetLastError();
                        Print("خطأ في إغلاق الصفقة ", OrderTicket(),
                              " - كود الخطأ: ", error,
                              " - الوصف: ", ErrorDescription(error));
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| إغلاق جميع الصفقات المفتوحة                                      |
//+------------------------------------------------------------------+
void CloseAllOrders()
{
    CloseOrders(true, true);
}

//+------------------------------------------------------------------+
//| دالة للتعامل مع الأحداث الخاصة                                   |
//+------------------------------------------------------------------+
void OnTimer()
{
    // يمكن استخدام هذه الدالة لمراقبة دورية أو تنظيف البيانات
    // مثال: التحقق من انتهاء صلاحية الصفقات المعلقة
}

//+------------------------------------------------------------------+
//| دالة للتعامل مع أحداث الرسم البياني                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long& lparam, const double& dparam, const string& sparam)
{
    // يمكن استخدام هذه الدالة للتفاعل مع أحداث الرسم البياني
    // مثال: تغيير الإعدادات عبر واجهة المستخدم
}

//+------------------------------------------------------------------+
//| إعادة تعيين جميع مستويات Square of 9                            |
//+------------------------------------------------------------------+
void ResetAllSquareOf9Levels()
{
    for(int i = 0; i < 5; i++) {
        g_SquareLevels[i].buyLevel = 0;
        g_SquareLevels[i].sellLevel = 0;
        g_SquareLevels[i].isActive = false;
        g_SquareLevels[i].buyLevelUsed = false;
        g_SquareLevels[i].sellLevelUsed = false;
        g_SquareLevels[i].signalBarTime = 0;
        g_SquareLevels[i].indicatorName = "";
    }
    Print("تم إعادة تعيين جميع مستويات Square of 9");
}

//+------------------------------------------------------------------+
//| طباعة حالة مستويات Square of 9                                  |
//+------------------------------------------------------------------+
void PrintSquareOf9Status()
{
    Print("=== حالة مستويات Square of 9 ===");
    for(int i = 0; i < 5; i++) {
        if(g_SquareLevels[i].isActive) {
            Print("المؤشر ", (i+1), " (", g_SquareLevels[i].indicatorName, "):");
            Print("  مستوى الشراء: ", g_SquareLevels[i].buyLevel, " - مستخدم: ", (g_SquareLevels[i].buyLevelUsed ? "نعم" : "لا"));
            Print("  مستوى البيع: ", g_SquareLevels[i].sellLevel, " - مستخدم: ", (g_SquareLevels[i].sellLevelUsed ? "نعم" : "لا"));
        }
    }
}

//+------------------------------------------------------------------+
//| دالة مساعدة لطباعة حالة الإكسبرت                                 |
//+------------------------------------------------------------------+
void PrintEAStatus()
{
    Print("=== حالة الإكسبرت ===");
    Print("الصفقات المفتوحة: ", GetOpenTradesCount());
    Print("الربح الحالي: ", CalculateCurrentProfit());
    Print("الربح اليومي: ", g_DailyProfit);
    Print("السماح بالتداول: ", g_TradingAllowed ? "نعم" : "لا");
    Print("صفقة مضاعفة في الانتظار: ", g_PendingDoubleOrder ? "نعم" : "لا");
    Print("عدد الصفقات منذ آخر مضاعفة: ", g_TradesSinceLastDouble);

    // طباعة حالة مستويات Square of 9
    PrintSquareOf9Status();
}
